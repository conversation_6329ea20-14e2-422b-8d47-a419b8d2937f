<template>
  <view class="details-page">
    <view class="banner">
      <image
        src="https://dummyimage.com/750x680/3c9cff/fff"
        mode="scaleToFill"
        class="banner__img"
      />
    </view>
    <view class="details-body">
      <!-- 门店信息 -->
      <view class="shop-card">
        <view class="popup-header">
          <view class="info">
            <view class="name">{{ stylist.name }}</view>
            <RoleTagsVue />
            <view class="experience">11年经验</view>
            <view class="score">满意值 {{ stylist.score }}</view>
          </view>
        </view>
        <view class="shop-title-row">
          <view class="shop-title">
            御剪理享金牛店
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
          <view class="shop-address">
            金牛区营门口街道银河北街232号
            <text class="iconfont icon-icon_copy copy-btn" @tap="copyAddress"></text>
            <text class="distance">距你2.5km</text>
          </view>
        </view>
      </view>

      <view class="works-section">
        <view class="works-tabs" :class="[activeTab === 'works' ? 'bg-1' : 'bg-2']">
          <view class="tab" :class="{ active: activeTab === 'works' }" @tap="activeTab = 'works'">
            <text class="title">精选作品({{ works.length }})</text>
          </view>
          <view
            class="tab"
            :class="{ active: activeTab === 'comments' }"
            @tap="activeTab = 'comments'"
          >
            <text class="title">服务评价({{ comments.length }})</text>
          </view>
        </view>
        <view v-if="activeTab === 'works'" class="works-list">
          <view class="work-card" v-for="(work, idx) in works" :key="idx">
            <image :src="work.img" class="work-img" mode="aspectFill"></image>
            <view class="work-title">{{ work.title }}</view>
            <view class="work-tags">
              <view class="tag" v-for="tag in work.tags" :key="tag">{{ tag }}</view>
            </view>
          </view>
        </view>
        <view v-else class="comments-section">
          <view class="comment-filters">
            <view
              class="filter"
              v-for="(f, idx) in filters"
              :key="idx"
              :class="{ active: filterActive === idx }"
              @tap="filterActive = idx"
            >
              {{ f.text }}
              <text v-if="f.count">{{ f.count }}</text>
            </view>
          </view>
          <view class="comment-list">
            <view class="comment-card" v-for="(c, idx) in comments" :key="idx">
              <image :src="c.avatar" class="comment-avatar"></image>
              <view class="comment-header">
                <view class="comment-user-box">
                  <view class="comment-user">
                    <view class="name">{{ c.name }}</view>
                    <view class="phone">{{ c.phone }}</view>
                  </view>
                  <view class="comment-result center">
                    满意
                    <Expression />
                  </view>
                </view>

                <view class="comment-tags">
                  <view class="tag" v-for="tag in c.tags" :key="tag">{{ tag }}</view>
                </view>

                <view class="comment-content">{{ c.content }}</view>
                <view class="comment-footer">
                  <view class="shop">
                    <view class="tag center">
                      <image src="/static/images/icon-user.png" mode="widthFix" class="img"></image>
                    </view>
                    店长：{{ c.stylist }}
                  </view>
                  <view class="date">{{ c.date }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!--  -->

    <view class="bottom-bar">
      <view class="item">
        <image src="/static/images/icon-paiban-2.png" mode="widthFix" class="icon"></image>
        <view class="text">排班</view>
      </view>
      <button hover-class="button-hover" class="btn center">立即预约</button>
    </view>
  </view>
</template>

<script>
import RoleTagsVue from '@/components/roleTags/roleTags.vue'
import Expression from '@/components/expression/expression.vue'
export default {
  components: { RoleTagsVue, Expression },
  data() {
    return {
      activeTab: 'works',
      filterActive: 0,
      filters: [
        { text: '全部', count: 457 },
        { text: '有图', count: 23 },
        { text: '满意', count: 103 },
        { text: '一般', count: 3 },
        { text: '不满意', count: 0 },
      ],
      stylist: {
        avatar: 'https://dummyimage.com/80x80/ccc/fff',
        name: '郑老师',
        score: '9.9',
      },
      works: [
        {
          img: 'https://dummyimage.com/300x400/eee/aaa',
          title: '甜美大波浪',
          tags: ['修饰脸型', '减龄'],
        },
        {
          img: 'https://dummyimage.com/300x400/eee/aaa',
          title: '韩系三七分',
          tags: ['修饰脸型', '减龄'],
        },
        {
          img: 'https://dummyimage.com/300x400/eee/aaa',
          title: '氛围感锁骨发',
          tags: ['修饰脸型', '减龄'],
        },
        {
          img: 'https://dummyimage.com/300x400/eee/aaa',
          title: '温柔慵懒卷',
          tags: ['修饰脸型', '减龄'],
        },
      ],
      comments: [
        {
          avatar: 'https://dummyimage.com/80x80/ccc/fff',
          name: '用户789098',
          phone: '176****6079',
          tags: ['服务热情', '环境好', '性价比高'],
          content: '剪的超级好，会听顾客的想法，待人热情,服务好,环境好,下次还来！',
          stylist: '郑老师',
          date: '2025-05-15 12:00:00',
        },
        // 可继续补充更多评价数据
      ],
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    copyAddress() {
      uni.setClipboardData({ data: '金牛区营门口街道银河北街232号' })
    },
    reorder() {
      // 跳转到下单页面
    },
  },
}
</script>

<style scoped lang="scss">
.details-page {
  padding-bottom: 120rpx;
}
.banner {
  &__img {
    width: 100%;
    height: 680rpx;
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid #f0f0f0;
  .item {
    text-align: center;
    color: #666666;
    margin: 0 50rpx;
    .icon {
      width: 42rpx;
      height: 42rpx;
    }
    .text {
      font-weight: 400;
      font-size: 20rpx;
      color: #999999;
      margin-top: 6rpx;
    }
  }
  .btn {
    // width: 428rpx;
    flex: 1;
    height: 78rpx;
    background: #c9a063;
    border-radius: 39rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
  }
}
.details-body {
  position: relative;
  .popup-header {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 20rpx;
    .info {
      display: flex;
      align-items: center;
      gap: 10rpx;
      .name {
        font-size: 32rpx;
        font-weight: 600;
        color: #222;
      }
      .score {
        color: #a27630;
        font-size: 24rpx;
        background: linear-gradient(-34deg, #ffefd7, #ffffff, #e2c99e);
        border-radius: 6rpx;
      }
      .experience {
        padding: 6rpx 12rpx;
        border: 1rpx solid #a27630;
        font-weight: 300;
        font-size: 18rpx;
        color: #a27630;
        border-radius: 6rpx;
      }
    }
  }
  .shop-card {
    position: relative;
    z-index: 9;
    width: 100%;
    height: 224rpx;
    background: linear-gradient(0deg, #fffbf4, #ffffff, #ffffff);
    border-radius: 56rpx 56rpx 0rpx 0rpx;
    margin-top: -80rpx;
    padding: 40rpx 70rpx;
    .iconfont {
      font-size: 28rpx;
      color: #666;
      margin-left: 10rpx;
    }

    .shop-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #666;
    }
    .shop-address {
      color: #999;
      font-size: 20rpx;
      font-size: 400;
      margin-top: 12rpx;
      display: flex;
      align-items: center;
    }
    .copy-btn {
      font-size: 28rpx;
      color: #999;
      margin-left: 10rpx;
    }

    .distance {
      color: #666666;
      margin-left: 8rpx;
      font-size: 26rpx;
    }
  }

  .works-section {
    border-radius: 32rpx;
    // padding: 32rpx 24rpx;
    .bg-1 {
      background: url('/static/images/bg-1.png') center center no-repeat !important;
      background-size: 100% 108rpx !important;
    }
    .bg-2 {
      background: url('/static/images/bg-2.png') top left no-repeat !important;
      background-size: 100% 108rpx !important;
    }
    .works-tabs {
      width: 100%;
      display: flex;
      position: relative;
      height: 108rpx;
      .tab {
        flex: 1;
        text-align: center;
        font-size: 30rpx;
        font-weight: 500;
        line-height: 108rpx;
        position: relative;
        z-index: 2;
      }
      .active {
        .title {
          font-weight: bold;
          color: #333333;
          background: url('http://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
            no-repeat;
          background-size: 100% 34rpx;
          background-position: bottom center;
          padding-bottom: 16rpx;
        }
      }
    }
    .works-list {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 32rpx 24rpx;
      background-color: white;
      padding: 20rpx 30rpx;
      .work-card {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        .work-img {
          width: 100%;
          height: 400rpx;
          border-radius: 16rpx;
          margin-bottom: 12rpx;
        }
        .work-title {
          margin-bottom: 12rpx;
          font-weight: 400;
          font-size: 26rpx;
          color: #333333;
        }
        .work-tags {
          display: flex;
          gap: 8rpx;
          .tag {
            font-weight: 400;
            font-size: 20rpx;
            color: #c9a063;
            padding: 4rpx 12rpx;
            background: #fff4e3;
            border-radius: 8rpx;
          }
        }
      }
    }
    .comments-section {
      background-color: white;
      padding: 20rpx 30rpx;
      .comment-filters {
        display: flex;
        gap: 16rpx;
        margin-bottom: 24rpx;
        .filter {
          background: white;
          color: #c9a063;
          font-size: 22rpx;
          border-radius: 32rpx;
          padding: 8rpx 24rpx;
          font-weight: 500;
          border: 1px solid #c9a063;
          &.active {
            background: #c9a063;
            color: #fff;
          }
        }
      }
      .comment-list {
        .comment-card {
          padding: 30rpx;
          margin-bottom: 24rpx;
          display: flex;
          border-bottom: 1rpx solid #fafafa;
          .comment-avatar {
            width: 68rpx;
            height: 68rpx;
            border-radius: 50%;
            margin-right: 20rpx;
            flex-shrink: 0;
          }
          .comment-header {
            .comment-user {
              &-box {
                display: flex;
                align-items: center;
              }
              flex: 1;
              .name {
                font-weight: 500;
                font-size: 24rpx;
                color: #333333;
                margin-bottom: 10rpx;
              }
              .phone {
                font-weight: 400;
                font-size: 22rpx;
                color: #666666;
              }
            }
            .comment-result {
              font-weight: 400;
              font-size: 24rpx;
              color: #999999;
              gap: 10rpx;
            }
          }
          .comment-tags {
            display: flex;
            gap: 12rpx;
            margin: 12rpx 0;
            .tag {
              background: #eaeaea;
              color: #adadad;
              font-size: 18rpx;
              padding: 10rpx 20rpx;
              border-radius: 20rpx;
            }
          }
          .comment-content {
            font-weight: 400;
            font-size: 20rpx;
            color: #666666;
            margin-bottom: 20rpx;
          }
          .comment-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            .shop {
              background: #f0f0f0;
              font-weight: 400;
              font-size: 20rpx;
              color: #adadad;
              font-size: 18rpx;
              border-radius: 8rpx;
              padding: 4rpx 12rpx;
              display: flex;
              align-items: center;
              .tag {
                width: 23rpx;
                height: 23rpx;
                background: #c9a063;
                border-radius: 4rpx;
                margin-right: 10rpx;

                .img {
                  width: 12rpx;
                  height: 16rpx;
                }
              }
            }
            .date {
              font-weight: 400;
              font-size: 20rpx;
              color: #adadad;
            }
          }
        }
      }
    }
  }
}
</style>
