<template>
  <view class="exchange-store">
    <!-- 御享值 -->
    <view class="exchange-store__top">
      <view class="exchange-store__coin">
        <image
          class="exchange-store__coin-img"
          src="/static/images/icon-jifen.png"
          mode="aspectFill"
        />
        <view class="">
          <view class="exchange-store__coin-label">
            我的御享值
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
          <view class="exchange-store__score">21</view>
        </view>
      </view>
      <button class="exchange-store__history-btn" hover-class="btnHoverClass">兑换记录</button>
    </view>
    <!-- 商品列表 -->
    <view class="exchange-store__grid">
      <!-- 商品卡片1 -->
      <view class="exchange-store__card" v-for="item in 3" :key="item">
        <image
          class="exchange-store__card-img"
          src="https://dummyimage.com/340x340/eeeeee/aaa&text=商品1"
          mode="aspectFit"
        />
        <view class="exchange-store__card-wrap">
          <view class="exchange-store__card-title">施华蔻洗发水</view>
          <view class="exchange-store__card-footer">
            <view class="exchange-store__card-price">
              <text class="exchange-store__card-price-yuan">88.6</text>
              <!-- <text class="exchange-store__card-price-plus"></text> -->
              <text class="exchange-store__card-price-score">+60</text>
            </view>
            <button class="exchange-store__card-btn" hover-class="btnHoverClass">兑换</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default { name: 'ExchangeStore' }
</script>

<style lang="scss" scoped>
.exchange-store {
  min-height: 100vh;
  background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  padding: 28rpx;
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  &__coin {
    display: flex;
    align-items: center;
  }
  &__coin-img {
    width: 68rpx;
    height: 68rpx;
    margin-right: 18rpx;
    vertical-align: middle;
  }
  &__coin-label {
    font-size: 26rpx;
    color: #333;
    margin-right: 4rpx;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
    .iconfont {
      color: #666;
      font-size: 22rpx;
      padding-top: 4rpx;
    }
  }

  &__score {
    font-weight: 600;
    font-size: 36rpx;
    color: #333333;
  }
  &__history-btn {
    font-weight: 500;
    font-size: 24rpx;
    color: #ffffff;
    background: #c9a063;
    border-radius: 36rpx;
    padding: 12rpx 28rpx;
  }

  &__grid {
    margin-top: 40rpx;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    column-gap: 10rpx;
    row-gap: 20rpx;
  }
  &__card {
    width: 100%;
    background: #ffffff;
    border-radius: 18rpx;
    &-wrap {
      padding: 20rpx;
    }
  }
  &__card-img {
    width: 100%;
    height: 340rpx;
    border-radius: 18rpx 18rpx 0 0;
  }
  &__card-title {
    margin: 20rpx 0 30rpx 0;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
  }
  &__card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &__card-price {
    font-weight: 600;
    font-size: 26rpx;
    color: #c9a063;
  }
  &__card-price-yuan {
    &::after {
      content: '元';
      font-size: 20rpx;
    }
  }
  &__card-price-plus {
  }
  &__card-price-score {
    &::after {
      content: '积分';
      font-size: 20rpx;
    }
  }
  &__card-btn {
    background: #c9a063;
    border-radius: 36rpx;
    font-weight: 400;
    font-size: 26rpx;
    color: #ffffff;
    padding: 8rpx 28rpx;
  }
}
</style>
