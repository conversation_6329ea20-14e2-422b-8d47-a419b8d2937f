<template>
  <view class="server-page">
    <view class="comment-filter">
      <view class="comment-filter__tabs">
        <view class="comment-filter__tab comment-filter__tab--active">全部 457</view>
        <view class="comment-filter__tab">有图 23</view>
        <view class="comment-filter__tab">满意 103</view>
        <view class="comment-filter__tab">一般 3</view>
        <view class="comment-filter__tab">不满意 0</view>
      </view>
      <view class="comment-filter__tags">
        <view class="comment-filter__tag">服务热情 98</view>
        <view class="comment-filter__tag">环境好 55</view>
        <view class="comment-filter__tag">性价比高 88</view>
        <view class="comment-filter__tag">剪发效果好 56</view>
      </view>
    </view>
    <view class="store-detail__comments">
      <view class="store-detail__comment-item" v-for="item in 6" :key="item">
        <image src="/static/images/icon-tag-other.png" class="store-detail__comment-avatar"></image>
        <view class="store-detail__comment-content">
          <view class="store-detail__comment-header">
            <view class="store-detail__comment-user">
              <view class="user">用户789098</view>
              <view class="id">ID: 123***7888</view>
            </view>
            <view class="store-detail__comment-satisfy">
              <text>满意</text>
              <Expression />
            </view>
          </view>
          <view class="store-detail__comment-tags">
            <text class="store-detail__comment-tag">服务热情</text>
            <text class="store-detail__comment-tag">环境好</text>
            <text class="store-detail__comment-tag">性价比高</text>
          </view>
          <view class="store-detail__comment-text">
            剪的超级好，会听顾客的想法，待人热情,服务好,环境好,下次还来！
          </view>
          <view class="store-detail__comment-footer">
            <view class="store-detail__comment-name">
              <view class="tag center">
                <image src="/static/images/icon-user.png" mode="widthFix" class="img"></image>
              </view>
              店长：郑老师
            </view>
            <view class="store-detail__comment-time">2025-05-15 12:00:00</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import Expression from '@/components/expression/expression.vue'
export default {
  name: 'CommentFilter',
  components: { Expression }
}
</script>

<style lang="scss" scoped>
.server-page {
}
.comment-filter {
  margin-top: 10rpx;
  padding: 20rpx 30rpx;
  background: white;
  &__tabs {
    display: flex;
    align-items: center;
    column-gap: 20rpx;
  }
  &__tab {
    padding: 10rpx 18rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #d19e58;
    border-radius: 36rpx;
    border: 1px solid #d19e58;
    &--active {
      background: #d19e58;
      color: #ffffff;
    }
  }

  &__tags {
    display: flex;
    align-items: center;
    column-gap: 20rpx;
    margin-top: 20rpx;
  }

  &__tag {
    padding: 10rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #adadad;
    background: #fafafa;
    border-radius: 20rpx;
  }
}
.store-detail__comments {
  margin-top: 10rpx;
  background-color: white;
  padding: 40rpx 30rpx;
  .store-detail__comment-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 100rpx;
    &:last-child {
      margin-bottom: 0;
    }
    .store-detail__comment-avatar {
      width: 68rpx;
      height: 68rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .store-detail__comment-content {
      flex: 1;
      .store-detail__comment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .user {
          font-weight: 500;
          font-size: 24rpx;
          color: #333333;
          margin-bottom: 10rpx;
        }
        .id {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
        }
      }
      .store-detail__comment-satisfy {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        display: flex;
        align-items: center;
        column-gap: 8rpx;
      }
      .store-detail__comment-tags {
        display: flex;
        align-items: center;
        column-gap: 20rpx;
        margin: 20rpx 0;
        .store-detail__comment-tag {
          background: #fafafa;
          border-radius: 20rpx;
          padding: 4rpx 12rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
      .store-detail__comment-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        margin-bottom: 20rpx;
      }
      .store-detail__comment-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store-detail__comment-name {
          display: flex;
          align-items: center;
          column-gap: 8rpx;
          .tag {
            width: 23rpx;
            height: 23rpx;
            background: #c9a063;
            border-radius: 4rpx;
            .img {
              width: 12rpx;
              height: 16rpx;
            }
          }
          background: #fafafa;
          border-radius: 8rpx;
          padding: 10rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
        .store-detail__comment-time {
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
    }
  }
}
</style>
